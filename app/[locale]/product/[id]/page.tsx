'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import { addToCart as addToSessionCart } from '../../../../lib/session-cart';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';

export default function ProductDetailPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const id = (params?.id || '') as string;
  const router = useRouter();
  const [product, setProduct] = useState<ProductWithDetails | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [whatsappSettings, setWhatsappSettings] = useState({
    businessNumber: '+966 599252259',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  const fetchProductDetails = useCallback(async (productId: string) => {
    try {
      setLoading(true);
      console.log('🚀 Fetching product details for ID:', productId);
      console.log('🌐 API URL:', `/api/products/${productId}`);

      // جلب تفاصيل المنتج
      const productResponse = await fetch(`/api/products/${productId}`);
      if (productResponse.ok) {
        const productResult = await productResponse.json();
        console.log('📦 استجابة API المنتج:', productResult);

        if (productResult.success && productResult.data) {
          const productData = productResult.data;
          setProduct(productData);

          // جلب بيانات الفئة الرئيسية
          if (productData.category_id) {
            const categoriesResponse = await fetch(`/api/categories?id=${productData.category_id}`);
            if (categoriesResponse.ok) {
              const categoriesResult = await categoriesResponse.json();
              console.log('📦 استجابة API الفئة:', categoriesResult);

              if (categoriesResult.success && categoriesResult.data) {
                setCategory(categoriesResult.data);
              }
            }
          }

          // جلب بيانات الفئة الفرعية
          if (productData.subcategory_id) {
            const subcategoriesResponse = await fetch(`/api/subcategories?id=${productData.subcategory_id}`);
            if (subcategoriesResponse.ok) {
              const subcategoriesResult = await subcategoriesResponse.json();
              console.log('📦 استجابة API الفئة الفرعية:', subcategoriesResult);

              if (subcategoriesResult.success && subcategoriesResult.data) {
                setSubcategory(subcategoriesResult.data);
              }
            }
          }
        } else {
          console.error('❌ فشل في جلب تفاصيل المنتج:', productResult);
          router.push(`/${locale}/products`);
          return;
        }
      } else {
        console.error('❌ خطأ في استجابة API المنتج');
        router.push(`/${locale}/products`);
        return;
      }
    } catch (error) {
      console.error('❌ خطأ في جلب تفاصيل المنتج:', error);
      setProduct(null);
      setCategory(null);
      setSubcategory(null);
    } finally {
      setLoading(false);
    }
  }, [router, locale]);

  useEffect(() => {
    console.log('🔍 Product ID from params:', id);
    console.log('🔍 Full params object:', params);
    if (id) {
      fetchProductDetails(id);
    }
  }, [id, params, fetchProductDetails]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  useEffect(() => {
    // قراءة إعدادات الواتساب من الإعدادات
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
        if (settings.communicationSettings?.whatsapp) {
          setWhatsappSettings(prev => ({
            ...prev,
            ...settings.communicationSettings.whatsapp
          }));
        }
      } catch (error) {
        console.log('Using default WhatsApp settings:', error);
      }
    }
  }, []);



  const addToCart = () => {
    if (!product || !product.is_available) return;

    // استخدام النظام الجديد للجلسات
    addToSessionCart({
      id: product.id,
      title: locale === 'ar' ? product.title_ar : product.title,
      titleAr: product.title_ar,
      image: product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة',
      price: product.price,
      quantity: quantity
    });

    setShowToast(true);
  };

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">{t('productDetails.loading')}</p>
          </div>
        </div>
        <Footer locale={locale} />
      </>
    );
  }

  if (!product) {
    return (
      <>
        <Navbar locale={locale} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('productDetails.notFound')}</h1>
            <button
              onClick={() => router.push(`/${locale}/products`)}
              className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('productDetails.backToProducts')}
            </button>
          </div>
        </div>
        <Footer locale={locale} />
      </>
    );
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  const productFeatures = product.features || [];
  const productSpecs = product.specifications || [];

  return (
    <>
      <Navbar locale={locale} />
      
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-8">
            <a href={`/${locale}`} className="hover:text-primary">{t('home')}</a>
            <span>/</span>
            <a href={`/${locale}/products`} className="hover:text-primary">{t('products')}</a>
            {category && (
              <>
                <span>/</span>
                <a
                  href={`/${locale}/category/${category.id}`}
                  className="hover:text-primary"
                >
                  {locale === 'ar' ? category.name_ar : category.name}
                </a>
              </>
            )}
            {subcategory && (
              <>
                <span>/</span>
                <a
                  href={`/${locale}/subcategory/${subcategory.id}`}
                  className="hover:text-primary"
                >
                  {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                </a>
              </>
            )}
            <span>/</span>
            <span className="text-primary font-medium">{productTitle}</span>
          </nav>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
              {/* Product Images */}
              <div className="space-y-4">
                <div className="aspect-square rounded-xl overflow-hidden bg-gray-100 relative">
                  <Image
                    src={product.images?.[selectedImageIndex]?.image_url || '/api/placeholder?width=600&height=600&text=لا توجد صورة'}
                    alt={productTitle}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/api/placeholder?width=600&height=600&text=خطأ في الصورة';
                    }}
                  />
                </div>

                {product.images && product.images.length > 1 && (
                  <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors relative ${
                          selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                        }`}
                      >
                        <Image
                          src={image.image_url || '/api/placeholder?width=80&height=80&text=صورة'}
                          alt={`${productTitle} ${index + 1}`}
                          fill
                          className="object-cover"
                          sizes="80px"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/api/placeholder?width=80&height=80&text=خطأ';
                          }}
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">{productTitle}</h1>
                  
                  {/* Category & Subcategory */}
                  <div className="flex items-center space-x-2 space-x-reverse mt-4">
                    {category && (
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                        {locale === 'ar' ? category.name_ar : category.name}
                      </span>
                    )}
                    {subcategory && (
                      <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                        {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                      </span>
                    )}
                  </div>
                </div>

                {/* Price */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="text-3xl font-bold text-primary">{product.price} {t('currency')}</span>
                  {product.original_price && product.original_price > product.price && (
                    <span className="text-xl text-gray-500 line-through">{product.original_price} {t('currency')}</span>
                  )}
                </div>

                {/* Availability */}
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className={`w-3 h-3 rounded-full ${product.is_available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`font-medium ${product.is_available ? 'text-green-600' : 'text-red-600'}`}>
                    {product.is_available ? t('available') : t('unavailable')}
                  </span>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.description')}</h3>
                  <p className="text-gray-600 leading-relaxed">{productDescription}</p>
                </div>

                {/* Features */}
                {productFeatures && productFeatures.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.features')}</h3>
                    <ul className="space-y-2">
                      {productFeatures.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2 space-x-reverse">
                          <i className="ri-check-line text-green-500"></i>
                          <span className="text-gray-600">
                            {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Specifications */}
                {productSpecs && productSpecs.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('productDetails.specifications')}</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <dl className="space-y-2">
                        {productSpecs.map((spec, index) => (
                          <div key={index} className="flex justify-between">
                            <dt className="font-medium text-gray-700">
                              {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}:
                            </dt>
                            <dd className="text-gray-600">
                              {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                            </dd>
                          </div>
                        ))}
                      </dl>
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="font-medium text-gray-700">{t('quantity')}:</span>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-subtract-line"></i>
                    </button>
                    <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-add-line"></i>
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-6 border-t space-y-3">
                  <button
                    onClick={addToCart}
                    disabled={!product.is_available}
                    className={`w-full py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 ${
                      product.is_available
                        ? 'bg-primary hover:bg-primary/90 text-white hover:shadow-lg transform hover:-translate-y-0.5'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <i className="ri-shopping-cart-2-line text-xl"></i>
                    <span>{product.is_available ? t('addToCart') : t('unavailable')}</span>
                  </button>
                  
                  <a
                    href={`https://wa.me/${whatsappSettings.businessNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(
                      // رسالة الترحيب
                      (locale === 'ar' ? whatsappSettings.welcomeMessageAr : whatsappSettings.welcomeMessage) +
                      '\n\n' +
                      // تفاصيل المنتج
                      (locale === 'ar' ? 'أريد الاستفسار عن هذا المنتج:' : 'I would like to inquire about this product:') +
                      '\n\n*' + productTitle + '*\n\n' +
                      (product.price > 0 ? `${locale === 'ar' ? 'السعر:' : 'Price:'} ${product.price} ${locale === 'ar' ? 'ر.س' : 'SAR'}\n\n` : '') +
                      productDescription + '\n\n' +
                      // رابط المنتج
                      (locale === 'ar' ? 'رابط المنتج:' : 'Product link:') + '\n' +
                      `${window.location.origin}/${locale}/product/${id}`
                    )}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors"
                  >
                    <i className="ri-whatsapp-line text-xl"></i>
                    <span>
                      {locale === 'ar' ? 'تواصل معنا عبر واتساب' : 'Contact us via WhatsApp'}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 space-x-reverse animate-slide-up">
          <i className="ri-check-line text-xl"></i>
          <span>{t('cartMessages.addedToCart')}</span>
        </div>
      )}
    </>
  );
}
